
import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
  Dimensions,
  Platform
} from 'react-native';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { IconSymbol } from '@/components/IconSymbol';
import { colors, commonStyles, buttonStyles, shadowStyles, fontFamily } from '@/styles/commonStyles';
import * as Location from 'expo-location';
import Constants from 'expo-constants';

// Conditional import for expo-maps (only works in development builds)
let MapView: any = null;
let Marker: any = null;

try {
  const expoMaps = require('expo-maps');
  MapView = expoMaps.MapView;
  Marker = expoMaps.Marker;
} catch (error) {
  console.log('expo-maps not available in Expo Go');
}
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  runOnJS,
  interpolate,
  Extrapolate,
} from 'react-native-reanimated';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import i18n from '@/localization/i18n';

const { height: screenHeight, width: screenWidth } = Dimensions.get('window');
const SLIDER_MIN_HEIGHT = 120;
const SLIDER_MAX_HEIGHT = screenHeight * 0.65;

interface BusVariety {
  id: string;
  name: string;
  nextStation: string;
  eta: string;
  latitude: number;
  longitude: number;
  passengers: number;
  capacity: number;
  status: 'In Service' | 'At Terminal' | 'Out of Service';
}

export default function TrackingMap() {
  const router = useRouter();
  const { city, cityName, line } = useLocalSearchParams();
  const [locationPermission, setLocationPermission] = useState<boolean | null>(null);
  const [userLocation, setUserLocation] = useState<any>(null);
  const [selectedVariety, setSelectedVariety] = useState<string | null>(null);
  const [busVarieties, setBusVarieties] = useState<BusVariety[]>([]);
  const [refreshing, setRefreshing] = useState(false);

  // Check if we're running in Expo Go
  const isExpoGo = Constants.appOwnership === 'expo';

  // Animation values
  const sliderHeight = useSharedValue(SLIDER_MAX_HEIGHT);
  const isSliderVisible = useSharedValue(true);
  const mapOpacity = useSharedValue(1);

  // Mock bus varieties data
  const mockBusVarieties: BusVariety[] = [
    {
      id: `${line}-A`,
      name: `${line}-A`,
      nextStation: 'Central Station',
      eta: '5 min',
      latitude: 33.5731,
      longitude: -7.5898,
      passengers: 15,
      capacity: 40,
      status: 'In Service'
    },
    {
      id: `${line}-B`,
      name: `${line}-B`,
      nextStation: 'University Campus',
      eta: '8 min',
      latitude: 33.5831,
      longitude: -7.5998,
      passengers: 28,
      capacity: 40,
      status: 'In Service'
    },
    {
      id: `${line}-C`,
      name: `${line}-C`,
      nextStation: 'Airport Terminal',
      eta: '12 min',
      latitude: 33.5631,
      longitude: -7.5798,
      passengers: 35,
      capacity: 40,
      status: 'In Service'
    },
    {
      id: `${line}-D`,
      name: `${line}-D`,
      nextStation: 'Mall Complex',
      eta: '15 min',
      latitude: 33.5531,
      longitude: -7.5698,
      passengers: 0,
      capacity: 40,
      status: 'At Terminal'
    },
  ];

  useEffect(() => {
    requestLocationPermission();
    setBusVarieties(mockBusVarieties);
    setSelectedVariety(mockBusVarieties[0].id);

    // Start live location updates every 15 seconds
    const locationInterval = setInterval(() => {
      updateBusLocations();
    }, 15000);

    return () => clearInterval(locationInterval);
  }, []);

  const requestLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();

      if (status === 'granted') {
        setLocationPermission(true);
        console.log('Location permission granted');

        const location = await Location.getCurrentPositionAsync({});
        setUserLocation(location.coords);
        console.log('User location:', location.coords);
      } else {
        setLocationPermission(false);
        Alert.alert(
          'Location Permission Required',
          'This app needs access to your location to show nearby buses and your position on the map.',
          [
            { text: i18n.t('cancel'), style: 'cancel' },
            { text: 'Retry', onPress: requestLocationPermission }
          ]
        );
      }
    } catch (error) {
      console.error('Error requesting location permission:', error);
      setLocationPermission(false);
    }
  };

  const updateBusLocations = () => {
    console.log('Updating bus locations (every 15 seconds)');
    setBusVarieties(prevVarieties =>
      prevVarieties.map(variety => ({
        ...variety,
        eta: `${Math.max(1, parseInt(variety.eta) - 1)} min`,
        passengers: Math.min(variety.capacity, variety.passengers + Math.floor(Math.random() * 2)),
        latitude: variety.latitude + (Math.random() - 0.5) * 0.005,
        longitude: variety.longitude + (Math.random() - 0.5) * 0.005,
      }))
    );
  };

  const handleMapPress = () => {
    console.log('Map pressed - toggling slider');
    const newVisibility = !isSliderVisible.value;
    isSliderVisible.value = newVisibility;

    sliderHeight.value = withSpring(
      newVisibility ? SLIDER_MAX_HEIGHT : SLIDER_MIN_HEIGHT,
      {
        damping: 20,
        stiffness: 120,
      },
      (finished) => {
        if (finished) {
          console.log('Slider animation completed');
        }
      }
    );
  };

  const handleVarietySelect = (varietyId: string) => {
    console.log('Selected bus variety:', varietyId);
    setSelectedVariety(varietyId);

    // Animate map update
    mapOpacity.value = withTiming(0.7, { duration: 200 }, () => {
      runOnJS(() => {
        console.log('Map updated to show bus:', varietyId);
      })();
      mapOpacity.value = withTiming(1, { duration: 200 });
    });
  };

  const handleRefresh = () => {
    console.log('Refreshing bus locations');
    setRefreshing(true);

    try {
      // Simulate API call
      setTimeout(() => {
        setRefreshing(false);
        updateBusLocations();
      }, 1500);
    } catch (error) {
      console.error('Refresh error:', error);
      setRefreshing(false);
    }
  };

  const handleBack = () => {
    console.log('Going back to line input');
    router.back();
  };

  // Gesture for slider
  const panGesture = Gesture.Pan()
    .onUpdate((event) => {
      const newHeight = SLIDER_MAX_HEIGHT - event.translationY;
      sliderHeight.value = Math.max(SLIDER_MIN_HEIGHT, Math.min(SLIDER_MAX_HEIGHT, newHeight));
    })
    .onEnd((event) => {
      const velocity = event.velocityY;
      const currentHeight = sliderHeight.value;

      if (velocity > 500 || currentHeight < SLIDER_MAX_HEIGHT * 0.4) {
        // Slide down
        sliderHeight.value = withSpring(SLIDER_MIN_HEIGHT, {
          damping: 20,
          stiffness: 120,
        });
        isSliderVisible.value = false;
      } else {
        // Slide up
        sliderHeight.value = withSpring(SLIDER_MAX_HEIGHT, {
          damping: 20,
          stiffness: 120,
        });
        isSliderVisible.value = true;
      }
    });

  // Animated styles
  const sliderAnimatedStyle = useAnimatedStyle(() => {
    return {
      height: sliderHeight.value,
      transform: [
        {
          translateY: interpolate(
            sliderHeight.value,
            [SLIDER_MIN_HEIGHT, SLIDER_MAX_HEIGHT],
            [0, 0],
            Extrapolate.CLAMP
          ),
        },
      ],
    };
  });

  const mapAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: mapOpacity.value,
    };
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'In Service': return colors.success;
      case 'At Terminal': return colors.accent;
      default: return colors.textSecondary;
    }
  };

  const getOccupancyColor = (passengers: number, capacity: number) => {
    const ratio = passengers / capacity;
    if (ratio < 0.5) return colors.success;
    if (ratio < 0.8) return colors.accent;
    return colors.error;
  };

  const selectedBus = busVarieties.find(variety => variety.id === selectedVariety);

  // Fallback Map Component for Expo Go
  const FallbackMap = () => (
    <View style={styles.fallbackMap}>
      <View style={styles.fallbackMapContent}>
        <IconSymbol name="map" size={48} color={colors.textSecondary} />
        <Text style={[styles.fallbackMapTitle, { color: colors.text }]}>
          Interactive Map
        </Text>
        <Text style={[styles.fallbackMapSubtitle, { color: colors.textSecondary }]}>
          Maps are available in development builds
        </Text>
        <Text style={[styles.fallbackMapInfo, { color: colors.textSecondary }]}>
          Currently showing {busVarieties.length} buses on line {line}
        </Text>

        {/* Show bus locations as a list */}
        <View style={styles.fallbackBusList}>
          {busVarieties.slice(0, 3).map((variety, index) => (
            <TouchableOpacity
              key={variety.id}
              style={[
                styles.fallbackBusItem,
                { backgroundColor: colors.card },
                selectedVariety === variety.id && { borderColor: colors.primary, borderWidth: 2 }
              ]}
              onPress={() => handleVarietySelect(variety.id)}
            >
              <View style={styles.fallbackBusIcon}>
                <IconSymbol name="bus" size={16} color={colors.primary} />
              </View>
              <View style={styles.fallbackBusInfo}>
                <Text style={[styles.fallbackBusName, { color: colors.text }]}>
                  {variety.name}
                </Text>
                <Text style={[styles.fallbackBusLocation, { color: colors.textSecondary }]}>
                  {variety.latitude.toFixed(4)}, {variety.longitude.toFixed(4)}
                </Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </View>
  );

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />

      <View style={[commonStyles.container, { backgroundColor: colors.background }]}>
        {/* Floating Back Button */}
        <TouchableOpacity
          style={styles.floatingBackButton}
          onPress={handleBack}
          activeOpacity={0.8}
        >
          <IconSymbol name="chevron.left" size={24} color={colors.card} />
        </TouchableOpacity>

        {/* Floating Refresh Button */}
        <TouchableOpacity
          style={styles.floatingRefreshButton}
          onPress={handleRefresh}
          activeOpacity={0.8}
        >
          <IconSymbol
            name="arrow.clockwise"
            size={20}
            color={colors.card}
            style={{ opacity: refreshing ? 0.5 : 1 }}
          />
        </TouchableOpacity>

        {/* Map Container */}
        <View style={styles.mapContainer}>
          <Animated.View style={[styles.mapContent, mapAnimatedStyle]}>
            <TouchableOpacity
              style={styles.mapPlaceholder}
              onPress={handleMapPress}
              activeOpacity={0.9}
            >
              {MapView && Marker && !isExpoGo ? (
                <MapView
                  style={styles.map}
                  initialCameraPosition={{
                    target: {
                      latitude: userLocation?.latitude || 33.5731,
                      longitude: userLocation?.longitude || -7.5898,
                    },
                    zoom: 15,
                  }}
                  showsUserLocation={true}
                  showsMyLocationButton={false}
                  showsCompass={false}
                  showsScale={false}
                  onPress={handleMapPress}
                >
                  {/* User Location Marker */}
                  {userLocation && (
                    <Marker
                      position={{
                        latitude: userLocation.latitude,
                        longitude: userLocation.longitude,
                      }}
                      title="Your Location"
                      description="You are here"
                    >
                      <View style={styles.userLocationMarker}>
                        <IconSymbol name="person.fill" size={20} color={colors.card} />
                      </View>
                    </Marker>
                  )}

                  {/* Bus Location Markers */}
                  {busVarieties.map((variety) => (
                    <Marker
                      key={variety.id}
                      position={{
                        latitude: variety.latitude,
                        longitude: variety.longitude,
                      }}
                      title={variety.name}
                      description={`Next: ${variety.nextStation} - ETA: ${variety.eta}`}
                      onPress={() => handleVarietySelect(variety.id)}
                    >
                      <View style={[
                        styles.busLocationMarker,
                        selectedVariety === variety.id && styles.selectedBusMarker
                      ]}>
                        <IconSymbol name="bus" size={16} color={colors.card} />
                      </View>
                    </Marker>
                  ))}
                </MapView>
              ) : (
                <FallbackMap />
              )}

              {/* Map Info Overlay - only show for real maps */}
              {MapView && Marker && !isExpoGo && (
                <View style={styles.mapInfoOverlay}>
                  <Text style={[styles.mapInfoText, { color: colors.text }]}>
                    Interactive Map - Tap to toggle bus list
                  </Text>
                  {userLocation && (
                    <Text style={[styles.mapInfoSubtext, { color: colors.textSecondary }]}>
                      Your location: {userLocation.latitude.toFixed(4)}, {userLocation.longitude.toFixed(4)}
                    </Text>
                  )}
                </View>
              )}
            </TouchableOpacity>
          </Animated.View>

          {/* Location Permission Banner */}
          {locationPermission === false && (
            <View style={styles.permissionBanner}>
              <IconSymbol name="location.slash" size={20} color={colors.error} />
              <Text style={[styles.permissionText, { color: colors.error }]}>
                {i18n.t('locationAccessDenied')}
              </Text>
              <TouchableOpacity onPress={requestLocationPermission}>
                <Text style={[styles.permissionAction, { color: colors.primary }]}>
                  {i18n.t('enable')}
                </Text>
              </TouchableOpacity>
            </View>
          )}
        </View>

        {/* Interactive Slider */}
        <GestureDetector gesture={panGesture}>
          <Animated.View style={[styles.sliderContainer, sliderAnimatedStyle]}>
            <View style={styles.sliderHandle}>
              <View style={[styles.handleBar, { backgroundColor: colors.textSecondary }]} />
            </View>

            <ScrollView
              style={styles.sliderContent}
              showsVerticalScrollIndicator={false}
              nestedScrollEnabled={true}
              scrollEventThrottle={16}
            >
              <View style={styles.sliderHeader}>
                <Text style={[styles.sliderTitle, { color: colors.text }]}>
                  {i18n.t('lineVarieties')} {line}
                </Text>
                <Text style={[styles.sliderSubtitle, { color: colors.textSecondary }]}>
                  {busVarieties.length} {i18n.t('busesInCity')} {cityName}
                </Text>
              </View>

              {/* Bus Varieties Vertical List (Fixed for Android) */}
              <View style={styles.varietiesContainer}>
                {busVarieties.map((variety, index) => (
                  <TouchableOpacity
                    key={variety.id}
                    style={[
                      styles.varietyCard,
                      { backgroundColor: colors.card },
                      selectedVariety === variety.id && {
                        borderColor: colors.primary,
                        borderWidth: 2,
                        backgroundColor: colors.primary + '10'
                      }
                    ]}
                    onPress={() => handleVarietySelect(variety.id)}
                    activeOpacity={0.7}
                  >
                    <View style={styles.varietyHeader}>
                      <View style={styles.varietyTitleRow}>
                        <Text style={[
                          styles.varietyName,
                          { color: colors.text },
                          selectedVariety === variety.id && { color: colors.primary, fontWeight: 'bold' }
                        ]}>
                          {variety.name}
                        </Text>
                        <View style={styles.varietyStatus}>
                          <View style={[styles.statusDot, { backgroundColor: getStatusColor(variety.status) }]} />
                          <Text style={[styles.statusText, { color: colors.textSecondary }]}>
                            {variety.status}
                          </Text>
                        </View>
                      </View>
                    </View>

                    <View style={styles.varietyInfo}>
                      <View style={styles.infoRow}>
                        <IconSymbol name="location.fill" size={16} color={colors.primary} />
                        <Text style={[styles.nextStation, { color: colors.text }]}>
                          {i18n.t('nextStop')}: {variety.nextStation}
                        </Text>
                      </View>
                      <View style={styles.infoRow}>
                        <IconSymbol name="clock.fill" size={16} color={colors.accent} />
                        <Text style={[styles.eta, { color: colors.accent }]}>
                          {i18n.t('arrivesIn')}: {variety.eta}
                        </Text>
                      </View>
                    </View>

                    <View style={styles.occupancyContainer}>
                      <View style={styles.occupancyInfo}>
                        <IconSymbol
                          name="person.2.fill"
                          size={16}
                          color={getOccupancyColor(variety.passengers, variety.capacity)}
                        />
                        <Text style={[styles.occupancyLabel, { color: colors.textSecondary }]}>
                          {i18n.t('occupancy')}:
                        </Text>
                        <Text style={[styles.occupancyText, { color: colors.text }]}>
                          {variety.passengers}/{variety.capacity} ({Math.round((variety.passengers / variety.capacity) * 100)}%)
                        </Text>
                      </View>
                      <View style={styles.occupancyBar}>
                        <View
                          style={[
                            styles.occupancyFill,
                            {
                              width: `${(variety.passengers / variety.capacity) * 100}%`,
                              backgroundColor: getOccupancyColor(variety.passengers, variety.capacity)
                            }
                          ]}
                        />
                      </View>
                    </View>
                  </TouchableOpacity>
                ))}
              </View>
            </ScrollView>
          </Animated.View>
        </GestureDetector>

        {/* Refreshing Indicator */}
        {refreshing && (
          <View style={styles.refreshingOverlay}>
            <View style={[styles.refreshingContainer, { backgroundColor: colors.card }]}>
              <IconSymbol name="arrow.clockwise" size={24} color={colors.primary} />
              <Text style={[styles.refreshingText, { color: colors.text }]}>
                {i18n.t('updatingLocations')}
              </Text>
            </View>
          </View>
        )}
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  floatingBackButton: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 60 : 40,
    left: 20,
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 100,
    ...shadowStyles.medium,
  },
  floatingRefreshButton: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 60 : 40,
    right: 20,
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 100,
    ...shadowStyles.medium,
  },
  mapContainer: {
    flex: 1,
    position: 'relative',
  },
  mapContent: {
    flex: 1,
  },
  map: {
    flex: 1,
    marginTop: Platform.OS === 'ios' ? 120 : 100,
  },
  mapPlaceholder: {
    flex: 1,
    position: 'relative',
  },
  mapInfoOverlay: {
    position: 'absolute',
    top: 16,
    left: 16,
    right: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    padding: 12,
    borderRadius: 8,
    ...shadowStyles.small,
  },
  mapInfoText: {
    fontSize: 14,
    fontWeight: '600',
    fontFamily: fontFamily.medium,
    textAlign: 'center',
  },
  mapInfoSubtext: {
    fontSize: 12,
    textAlign: 'center',
    marginTop: 4,
  },
  userLocationMarker: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: colors.card,
    ...shadowStyles.medium,
  },
  busLocationMarker: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: colors.accent,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: colors.card,
    ...shadowStyles.small,
  },
  selectedBusMarker: {
    backgroundColor: colors.primary,
    width: 32,
    height: 32,
    borderRadius: 16,
    borderWidth: 3,
    ...shadowStyles.medium,
  },
  mapText: {
    fontSize: 18,
    fontWeight: 'bold',
    fontFamily: fontFamily.bold,
    marginTop: 12,
  },
  mapSubtext: {
    fontSize: 12,
    textAlign: 'center',
    marginTop: 4,
    paddingHorizontal: 20,
  },
  locationIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
    padding: 8,
    backgroundColor: colors.background,
    borderRadius: 8,
  },
  userLocationIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  busLocationIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: colors.accent,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  locationText: {
    fontSize: 12,
    fontWeight: '500',
    fontFamily: fontFamily.medium,
  },
  permissionBanner: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 120 : 100,
    left: 16,
    right: 16,
    backgroundColor: colors.card,
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    ...shadowStyles.medium,
  },
  permissionText: {
    flex: 1,
    marginLeft: 8,
    fontSize: 14,
  },
  permissionAction: {
    fontSize: 14,
    fontWeight: 'bold',
    fontFamily: fontFamily.medium,
  },
  sliderContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: colors.card,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    ...shadowStyles.large,
    zIndex: 10,
  },
  sliderHandle: {
    alignItems: 'center',
    paddingVertical: 12,
  },
  handleBar: {
    width: 40,
    height: 4,
    borderRadius: 2,
  },
  sliderContent: {
    flex: 1,
    paddingHorizontal: 16,
    paddingBottom: Platform.OS === 'ios' ? 34 : 16,
  },
  sliderHeader: {
    marginBottom: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  sliderTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    fontFamily: fontFamily.bold,
    marginBottom: 4,
  },
  sliderSubtitle: {
    fontSize: 14,
  },
  varietiesContainer: {
    gap: 12,
  },
  varietyCard: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.border,
    ...shadowStyles.small,
  },
  varietyHeader: {
    marginBottom: 12,
  },
  varietyTitleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  varietyName: {
    fontSize: 18,
    fontWeight: '600',
    fontFamily: fontFamily.medium,
  },
  varietyStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  statusText: {
    fontSize: 12,
  },
  varietyInfo: {
    marginBottom: 12,
    gap: 8,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  nextStation: {
    fontSize: 14,
    flex: 1,
  },
  eta: {
    fontSize: 14,
    fontWeight: '600',
    fontFamily: fontFamily.medium,
    flex: 1,
  },
  occupancyContainer: {
    gap: 8,
  },
  occupancyInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  occupancyLabel: {
    fontSize: 12,
  },
  occupancyText: {
    fontSize: 12,
    fontWeight: '500',
    fontFamily: fontFamily.medium,
  },
  occupancyBar: {
    height: 6,
    backgroundColor: colors.border,
    borderRadius: 3,
  },
  occupancyFill: {
    height: '100%',
    borderRadius: 3,
  },
  refreshingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  refreshingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    ...shadowStyles.medium,
  },
  refreshingText: {
    fontSize: 16,
    marginLeft: 12,
    fontFamily: fontFamily.medium,
  },
  // Fallback Map Styles
  fallbackMap: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background,
    marginTop: Platform.OS === 'ios' ? 120 : 100,
  },
  fallbackMapContent: {
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  fallbackMapTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    fontFamily: fontFamily.bold,
    marginTop: 16,
    marginBottom: 8,
  },
  fallbackMapSubtitle: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 8,
  },
  fallbackMapInfo: {
    fontSize: 12,
    textAlign: 'center',
    marginBottom: 24,
  },
  fallbackBusList: {
    width: '100%',
    gap: 8,
  },
  fallbackBusItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.border,
  },
  fallbackBusIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  fallbackBusInfo: {
    flex: 1,
  },
  fallbackBusName: {
    fontSize: 14,
    fontWeight: '600',
    fontFamily: fontFamily.medium,
    marginBottom: 2,
  },
  fallbackBusLocation: {
    fontSize: 12,
  },
});

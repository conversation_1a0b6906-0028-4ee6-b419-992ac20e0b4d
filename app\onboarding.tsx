
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Dimensions,
  Platform,
} from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { IconSymbol } from '@/components/IconSymbol';
import { colors, commonStyles, buttonStyles, shadowStyles, fontFamily } from '@/styles/commonStyles';
import Animated, {
  FadeInDown,
  FadeInUp,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  runOnJS
} from 'react-native-reanimated';
import AsyncStorage from '@react-native-async-storage/async-storage';
import i18n from '@/localization/i18n';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export default function OnboardingScreen() {
  const router = useRouter();
  const [selectedUserType, setSelectedUserType] = useState<'driver' | 'passenger' | null>(null);
  const [showSteps, setShowSteps] = useState(false);

  const scaleDriver = useSharedValue(1);
  const scalePassenger = useSharedValue(1);
  const contentOpacity = useSharedValue(1);

  useEffect(() => {
    // Mark onboarding as seen
    AsyncStorage.setItem('hasSeenOnboarding', 'true');
  }, []);

  const handleUserTypeSelect = (userType: 'driver' | 'passenger') => {
    console.log('Selected user type:', userType);
    setSelectedUserType(userType);

    // Animate selection
    const scale = userType === 'driver' ? scaleDriver : scalePassenger;
    scale.value = withSpring(0.95, { duration: 100 }, () => {
      scale.value = withSpring(1, { duration: 100 });
    });

    // Show steps after animation
    setTimeout(() => {
      setShowSteps(true);
    }, 200);
  };

  const handleContinue = () => {
    console.log('Continuing with user type:', selectedUserType);

    // Add a small delay to ensure UI is ready
    setTimeout(() => {
      try {
        if (selectedUserType === 'driver') {
          router.push('/driver/register');
        } else {
          router.push('/passenger/city-selection');
        }
      } catch (error) {
        console.error('Navigation error:', error);
        // Fallback navigation
        try {
          if (selectedUserType === 'driver') {
            router.replace('/driver/register');
          } else {
            router.replace('/passenger/city-selection');
          }
        } catch (fallbackError) {
          console.error('Fallback navigation failed:', fallbackError);
        }
      }
    }, 100);
  };

  const handleBack = () => {
    if (showSteps) {
      setShowSteps(false);
      setSelectedUserType(null);
    } else {
      router.replace('/(tabs)/(home)');
    }
  };

  const driverAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scaleDriver.value }],
  }));

  const passengerAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scalePassenger.value }],
  }));

  const contentAnimatedStyle = useAnimatedStyle(() => ({
    opacity: contentOpacity.value,
  }));

  const driverSteps = [
    {
      icon: 'person.badge.plus',
      title: i18n.t('driverRegistration'),
      description: 'Register your account with bus company details'
    },
    {
      icon: 'checkmark.circle',
      title: i18n.t('otpVerification'),
      description: 'Verify your phone number with SMS code'
    },
    {
      icon: 'list.bullet',
      title: i18n.t('selectLine'),
      description: 'Choose your bus line for today\'s shift'
    },
    {
      icon: 'play.circle',
      title: i18n.t('startShift'),
      description: 'Begin tracking and start your work day'
    },
    {
      icon: 'location',
      title: 'Live Tracking',
      description: 'Your location is shared with passengers in real-time'
    }
  ];

  const passengerSteps = [
    {
      icon: 'location.circle',
      title: i18n.t('selectYourCity'),
      description: 'Choose the city where you want to track buses'
    },
    {
      icon: 'bus',
      title: i18n.t('selectBusLine'),
      description: 'Enter the bus line number you want to track'
    },
    {
      icon: 'map',
      title: i18n.t('realTimeTracking'),
      description: 'See live bus locations and arrival times'
    },
    {
      icon: 'clock',
      title: i18n.t('etaEstimates'),
      description: 'Get accurate arrival time predictions'
    },
    {
      icon: 'location.fill',
      title: 'Your Location',
      description: 'See your position relative to bus stops'
    }
  ];

  return (
    <>
      <Stack.Screen
        options={{
          title: i18n.t('welcome'),
          headerShown: false,
        }}
      />

      <Animated.View style={[commonStyles.container, contentAnimatedStyle]}>
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {!showSteps ? (
            // User Type Selection
            <>
              {/* Header */}
              <Animated.View
                entering={FadeInUp.delay(200).duration(600)}
                style={styles.headerSection}
              >
                <View style={styles.logoContainer}>
                  <IconSymbol name="bus" size={80} color={colors.primary} />
                </View>
                <Text style={[commonStyles.title, styles.welcomeTitle]}>
                  {i18n.t('welcome')}
                </Text>
                <Text style={[commonStyles.textSecondary, styles.welcomeSubtitle]}>
                  {i18n.t('chooseUserType')}
                </Text>
              </Animated.View>

              {/* User Type Cards */}
              <View style={styles.selectionContainer}>
                <Animated.View
                  entering={FadeInDown.delay(400).duration(600)}
                >
                  <Animated.View style={driverAnimatedStyle}>
                    <TouchableOpacity
                      style={[styles.userTypeCard, styles.driverCard]}
                      onPress={() => handleUserTypeSelect('driver')}
                      activeOpacity={0.8}
                    >
                      <View style={styles.cardIcon}>
                        <IconSymbol name="person.fill" size={48} color={colors.card} />
                      </View>
                      <Text style={[styles.cardTitle, { color: colors.card }]}>
                        {i18n.t('imADriver')}
                      </Text>
                      <Text style={[styles.cardDescription, { color: colors.card }]}>
                        {i18n.t('driverDescription')}
                      </Text>
                    </TouchableOpacity>
                  </Animated.View>
                </Animated.View>

                <Animated.View
                  entering={FadeInDown.delay(600).duration(600)}
                >
                  <Animated.View style={passengerAnimatedStyle}>
                    <TouchableOpacity
                      style={[styles.userTypeCard, styles.passengerCard]}
                      onPress={() => handleUserTypeSelect('passenger')}
                      activeOpacity={0.8}
                    >
                      <View style={styles.cardIcon}>
                        <IconSymbol name="location.fill" size={48} color={colors.card} />
                      </View>
                      <Text style={[styles.cardTitle, { color: colors.card }]}>
                        {i18n.t('imAPassenger')}
                      </Text>
                      <Text style={[styles.cardDescription, { color: colors.card }]}>
                        {i18n.t('passengerDescription')}
                      </Text>
                    </TouchableOpacity>
                  </Animated.View>
                </Animated.View>
              </View>
            </>
          ) : (
            // Steps View
            <>
              {/* Header */}
              <Animated.View
                entering={FadeInUp.delay(200).duration(600)}
                style={styles.stepsHeader}
              >
                <TouchableOpacity onPress={handleBack} style={styles.backButton}>
                  <IconSymbol name="chevron.left" size={24} color={colors.primary} />
                </TouchableOpacity>
                <Text style={[commonStyles.subtitle, styles.stepsTitle]}>
                  {selectedUserType === 'driver' ? i18n.t('driverSteps') : i18n.t('passengerSteps')}
                </Text>
                <Text style={[commonStyles.textSecondary, styles.stepsSubtitle]}>
                  {selectedUserType === 'driver' ? i18n.t('howToUseAsDriver') : i18n.t('howToUseAsPassenger')}
                </Text>
              </Animated.View>

              {/* Steps List */}
              <View style={styles.stepsContainer}>
                {(selectedUserType === 'driver' ? driverSteps : passengerSteps).map((step, index) => (
                  <Animated.View
                    key={index}
                    entering={FadeInDown.delay(300 + index * 100).duration(500)}
                    style={styles.stepCard}
                  >
                    <View style={styles.stepNumber}>
                      <Text style={[styles.stepNumberText, { color: colors.card }]}>
                        {index + 1}
                      </Text>
                    </View>
                    <View style={styles.stepIcon}>
                      <IconSymbol name={step.icon as any} size={24} color={colors.primary} />
                    </View>
                    <View style={styles.stepContent}>
                      <Text style={[styles.stepTitle, { color: colors.text }]}>
                        {step.title}
                      </Text>
                      <Text style={[styles.stepDescription, { color: colors.textSecondary }]}>
                        {step.description}
                      </Text>
                    </View>
                  </Animated.View>
                ))}
              </View>

              {/* Continue Button */}
              <Animated.View
                entering={FadeInUp.delay(800).duration(600)}
                style={styles.continueContainer}
              >
                <Animated.View
                  entering={FadeInUp.delay(800).duration(600)}
                >
                  <TouchableOpacity
                    style={[
                      buttonStyles.elevated,
                      selectedUserType === 'passenger' && { backgroundColor: colors.passenger }
                    ]}
                    onPress={handleContinue}
                    activeOpacity={0.8}
                  >
                    <Text style={[commonStyles.buttonText, styles.continueButtonText]}>
                      {selectedUserType === 'driver' ? 'Register as Driver' : 'Track your bus'}
                    </Text>
                    <IconSymbol name="arrow.right" size={20} color={colors.card} />
                  </TouchableOpacity>
                </Animated.View>
              </Animated.View>
            </>
          )}
        </ScrollView>
      </Animated.View>
    </>
  );
}

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: Platform.OS === 'ios' ? 40 : 48,
  },
  headerSection: {
    alignItems: 'center',
    paddingTop: Platform.OS === 'ios' ? 80 : 60,
    paddingHorizontal: 20,
    marginBottom: 40,
  },
  logoContainer: {
    width: 140,
    height: 140,
    borderRadius: 70,
    backgroundColor: colors.primary + '15',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 32,
    ...shadowStyles.medium,
  },
  welcomeTitle: {
    fontSize: 32,
    marginBottom: 16,
    color: colors.text,
    fontFamily: fontFamily.bold,
  },
  welcomeSubtitle: {
    textAlign: 'center',
    paddingHorizontal: 20,
    lineHeight: 22,
    fontSize: 16,
  },
  selectionContainer: {
    paddingHorizontal: 20,
    gap: 24,
  },
  userTypeCard: {
    borderRadius: 24,
    padding: 32,
    alignItems: 'center',
    minHeight: 200,
    ...shadowStyles.large,
  },
  driverCard: {
    backgroundColor: colors.primary,
  },
  passengerCard: {
    backgroundColor: colors.passenger,
  },
  cardIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  cardTitle: {
    fontSize: 24,
    fontWeight: Platform.OS === 'ios' ? '700' : 'bold',
    fontFamily: fontFamily.bold,
    marginBottom: 12,
    textAlign: 'center',
  },
  cardDescription: {
    fontSize: 16,
    opacity: 0.9,
    textAlign: 'center',
    lineHeight: 22,
    paddingHorizontal: 10,
  },
  stepsHeader: {
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingHorizontal: 20,
    marginBottom: 32,
  },
  backButton: {
    alignSelf: 'flex-start',
    padding: 8,
    marginLeft: -8,
    marginBottom: 20,
  },
  stepsTitle: {
    textAlign: 'left',
    marginBottom: 8,
    fontSize: 24,
  },
  stepsSubtitle: {
    textAlign: 'left',
    fontSize: 16,
  },
  stepsContainer: {
    paddingHorizontal: 20,
    gap: 16,
  },
  stepCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.card,
    borderRadius: 16,
    padding: 20,
    ...shadowStyles.small,
  },
  stepNumber: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  stepNumberText: {
    fontSize: 16,
    fontWeight: 'bold',
    fontFamily: fontFamily.bold,
  },
  stepIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: colors.primary + '15',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  stepContent: {
    flex: 1,
  },
  stepTitle: {
    fontSize: 16,
    fontWeight: '600',
    fontFamily: fontFamily.medium,
    marginBottom: 4,
  },
  stepDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  continueContainer: {
    paddingHorizontal: 20,
    paddingTop: 32,
  },
  continueButtonText: {
    fontSize: 18,
    marginRight: 8,
  },
});

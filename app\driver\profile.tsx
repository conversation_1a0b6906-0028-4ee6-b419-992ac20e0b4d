
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
  Platform,
  KeyboardAvoidingView,
} from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { IconSymbol } from '@/components/IconSymbol';
import { colors, commonStyles, buttonStyles, shadowStyles, fontFamily } from '@/styles/commonStyles';
import { cities, busCompanies } from '@/data/cities';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Animated, { FadeInDown, FadeInUp } from 'react-native-reanimated';
import i18n from '@/localization/i18n';

export default function DriverProfile() {
  const router = useRouter();
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [driverData, setDriverData] = useState({
    fullName: '',
    email: '',
    phoneNumber: '',
    busCompany: '',
    city: '',
    licenseNumber: '',
    busNumber: '',
    registrationDate: '',
  });

  useEffect(() => {
    loadDriverProfile();
  }, []);

  const loadDriverProfile = async () => {
    try {
      const profileData = await AsyncStorage.getItem('driverProfile');
      if (profileData) {
        const profile = JSON.parse(profileData);
        setDriverData(profile);
        console.log('Driver profile loaded:', profile);
      } else {
        Alert.alert(i18n.t('error'), 'Profile not found. Please register first.');
        router.replace('/driver/register');
      }
    } catch (error) {
      console.error('Error loading driver profile:', error);
      Alert.alert(i18n.t('error'), 'Failed to load profile.');
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setDriverData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSaveProfile = async () => {
    console.log('Saving profile changes');
    setLoading(true);

    try {
      await AsyncStorage.setItem('driverProfile', JSON.stringify(driverData));
      
      Alert.alert(
        i18n.t('success'),
        'Profile updated successfully!',
        [
          {
            text: i18n.t('ok'),
            onPress: () => setIsEditing(false)
          }
        ]
      );
    } catch (error) {
      console.error('Error saving profile:', error);
      Alert.alert(i18n.t('error'), 'Failed to save profile. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleCancelEdit = () => {
    console.log('Cancelling profile edit');
    setIsEditing(false);
    loadDriverProfile(); // Reload original data
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: i18n.t('cancel'), style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            try {
              // Clear driver session data but keep profile
              await AsyncStorage.removeItem('currentShift');
              await AsyncStorage.removeItem('isDriverVerified');
              
              console.log('Driver logged out');
              router.replace('/driver/login');
            } catch (error) {
              console.error('Error during logout:', error);
            }
          }
        }
      ]
    );
  };

  const handleBack = () => {
    console.log('Going back');
    router.back();
  };

  const getBusCompanyName = (companyId: string) => {
    const company = busCompanies.find(c => c.id === companyId);
    return company ? company.name : companyId;
  };

  const getCityName = (cityId: string) => {
    const city = cities.find(c => c.id === cityId);
    return city ? city.name : cityId;
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <>
      <Stack.Screen
        options={{
          title: i18n.t('profile'),
          headerShown: false,
        }}
      />
      
      <KeyboardAvoidingView 
        style={[commonStyles.container, { backgroundColor: colors.background }]}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* Custom Header */}
        <View style={styles.customHeader}>
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <IconSymbol name="chevron.left" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            {i18n.t('profile')}
          </Text>
          <TouchableOpacity 
            onPress={() => setIsEditing(!isEditing)}
            style={styles.editButton}
          >
            <IconSymbol 
              name={isEditing ? "xmark" : "pencil"} 
              size={20} 
              color={colors.primary} 
            />
          </TouchableOpacity>
        </View>

        <ScrollView 
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {/* Profile Header */}
          <Animated.View 
            entering={FadeInUp.delay(200).duration(500)}
            style={styles.profileHeader}
          >
            <View style={[styles.avatarContainer, { backgroundColor: colors.primary }]}>
              <IconSymbol name="person.fill" size={48} color={colors.card} />
            </View>
            <Text style={[styles.driverName, { color: colors.text }]}>
              {driverData.fullName || 'Driver Name'}
            </Text>
            <Text style={[styles.driverRole, { color: colors.textSecondary }]}>
              Bus Driver • {getBusCompanyName(driverData.busCompany)}
            </Text>
            <Text style={[styles.memberSince, { color: colors.textSecondary }]}>
              Member since {formatDate(driverData.registrationDate)}
            </Text>
          </Animated.View>

          {/* Profile Information */}
          <Animated.View 
            entering={FadeInDown.delay(400).duration(500)}
            style={styles.profileSection}
          >
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              {i18n.t('driverInfo')}
            </Text>

            <View style={styles.infoContainer}>
              <View style={styles.infoItem}>
                <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                  {i18n.t('fullName')}
                </Text>
                {isEditing ? (
                  <TextInput
                    style={[styles.infoInput, { backgroundColor: colors.card, color: colors.text }]}
                    value={driverData.fullName}
                    onChangeText={(value) => handleInputChange('fullName', value)}
                    placeholder={i18n.t('fullName')}
                    placeholderTextColor={colors.textSecondary}
                  />
                ) : (
                  <Text style={[styles.infoValue, { color: colors.text }]}>
                    {driverData.fullName || 'Not provided'}
                  </Text>
                )}
              </View>

              <View style={styles.infoItem}>
                <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                  {i18n.t('email')}
                </Text>
                {isEditing ? (
                  <TextInput
                    style={[styles.infoInput, { backgroundColor: colors.card, color: colors.text }]}
                    value={driverData.email}
                    onChangeText={(value) => handleInputChange('email', value)}
                    placeholder={i18n.t('email')}
                    placeholderTextColor={colors.textSecondary}
                    keyboardType="email-address"
                    autoCapitalize="none"
                  />
                ) : (
                  <Text style={[styles.infoValue, { color: colors.text }]}>
                    {driverData.email || 'Not provided'}
                  </Text>
                )}
              </View>

              <View style={styles.infoItem}>
                <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                  {i18n.t('phoneNumber')}
                </Text>
                {isEditing ? (
                  <TextInput
                    style={[styles.infoInput, { backgroundColor: colors.card, color: colors.text }]}
                    value={driverData.phoneNumber}
                    onChangeText={(value) => handleInputChange('phoneNumber', value)}
                    placeholder={i18n.t('phoneNumber')}
                    placeholderTextColor={colors.textSecondary}
                    keyboardType="phone-pad"
                  />
                ) : (
                  <Text style={[styles.infoValue, { color: colors.text }]}>
                    {driverData.phoneNumber || 'Not provided'}
                  </Text>
                )}
              </View>

              <View style={styles.infoItem}>
                <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                  {i18n.t('busCompany')}
                </Text>
                <Text style={[styles.infoValue, { color: colors.text }]}>
                  {getBusCompanyName(driverData.busCompany) || 'Not provided'}
                </Text>
              </View>

              <View style={styles.infoItem}>
                <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                  {i18n.t('city')}
                </Text>
                <Text style={[styles.infoValue, { color: colors.text }]}>
                  {getCityName(driverData.city) || 'Not provided'}
                </Text>
              </View>

              <View style={styles.infoItem}>
                <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                  {i18n.t('driverLicenseNumber')}
                </Text>
                {isEditing ? (
                  <TextInput
                    style={[styles.infoInput, { backgroundColor: colors.card, color: colors.text }]}
                    value={driverData.licenseNumber}
                    onChangeText={(value) => handleInputChange('licenseNumber', value)}
                    placeholder={i18n.t('driverLicenseNumber')}
                    placeholderTextColor={colors.textSecondary}
                    autoCapitalize="characters"
                  />
                ) : (
                  <Text style={[styles.infoValue, { color: colors.text }]}>
                    {driverData.licenseNumber || 'Not provided'}
                  </Text>
                )}
              </View>

              <View style={styles.infoItem}>
                <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                  {i18n.t('busNumber')}
                </Text>
                {isEditing ? (
                  <TextInput
                    style={[styles.infoInput, { backgroundColor: colors.card, color: colors.text }]}
                    value={driverData.busNumber}
                    onChangeText={(value) => handleInputChange('busNumber', value)}
                    placeholder={i18n.t('busNumber')}
                    placeholderTextColor={colors.textSecondary}
                  />
                ) : (
                  <Text style={[styles.infoValue, { color: colors.text }]}>
                    {driverData.busNumber || 'Not assigned'}
                  </Text>
                )}
              </View>
            </View>
          </Animated.View>

          {/* Action Buttons */}
          {isEditing ? (
            <Animated.View 
              entering={FadeInUp.delay(600).duration(500)}
              style={styles.editActions}
            >
              <TouchableOpacity
                style={[buttonStyles.outline, styles.cancelButton]}
                onPress={handleCancelEdit}
              >
                <Text style={[commonStyles.buttonTextOutline, { color: colors.textSecondary }]}>
                  {i18n.t('cancel')}
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[buttonStyles.primary, styles.saveButton, { opacity: loading ? 0.7 : 1 }]}
                onPress={handleSaveProfile}
                disabled={loading}
              >
                <Text style={[commonStyles.buttonText, { color: colors.card }]}>
                  {loading ? 'Saving...' : 'Save Changes'}
                </Text>
              </TouchableOpacity>
            </Animated.View>
          ) : (
            <Animated.View 
              entering={FadeInUp.delay(600).duration(500)}
              style={styles.profileActions}
            >
              <TouchableOpacity
                style={[styles.actionButton, { backgroundColor: colors.card }]}
                onPress={() => router.push('/driver/line-selection')}
              >
                <IconSymbol name="list.bullet" size={20} color={colors.primary} />
                <Text style={[styles.actionButtonText, { color: colors.text }]}>
                  Select Line
                </Text>
                <IconSymbol name="chevron.right" size={16} color={colors.textSecondary} />
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.actionButton, { backgroundColor: colors.card }]}
                onPress={() => router.push('/driver/active-shift')}
              >
                <IconSymbol name="play.circle" size={20} color={colors.success} />
                <Text style={[styles.actionButtonText, { color: colors.text }]}>
                  Active Shift
                </Text>
                <IconSymbol name="chevron.right" size={16} color={colors.textSecondary} />
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.actionButton, styles.logoutButton]}
                onPress={handleLogout}
              >
                <IconSymbol name="rectangle.portrait.and.arrow.right" size={20} color={colors.error} />
                <Text style={[styles.actionButtonText, { color: colors.error }]}>
                  Logout
                </Text>
                <IconSymbol name="chevron.right" size={16} color={colors.error} />
              </TouchableOpacity>
            </Animated.View>
          )}
        </ScrollView>
      </KeyboardAvoidingView>
    </>
  );
}

const styles = StyleSheet.create({
  customHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingHorizontal: 20,
    paddingBottom: 16,
    backgroundColor: colors.background,
    ...shadowStyles.small,
  },
  backButton: {
    padding: 8,
    marginLeft: -8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    fontFamily: fontFamily.bold,
    flex: 1,
    textAlign: 'center',
    marginHorizontal: 16,
  },
  editButton: {
    padding: 8,
    marginRight: -8,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 20,
    paddingBottom: Platform.OS === 'ios' ? 40 : 48,
  },
  profileHeader: {
    alignItems: 'center',
    marginBottom: 32,
    paddingVertical: 20,
  },
  avatarContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
    ...shadowStyles.medium,
  },
  driverName: {
    fontSize: 24,
    fontWeight: 'bold',
    fontFamily: fontFamily.bold,
    marginBottom: 4,
  },
  driverRole: {
    fontSize: 16,
    marginBottom: 4,
  },
  memberSince: {
    fontSize: 14,
  },
  profileSection: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    fontFamily: fontFamily.bold,
    marginBottom: 20,
  },
  infoContainer: {
    backgroundColor: colors.card,
    borderRadius: 16,
    padding: 20,
    ...shadowStyles.small,
  },
  infoItem: {
    marginBottom: 20,
  },
  infoLabel: {
    fontSize: 14,
    fontWeight: '500',
    fontFamily: fontFamily.medium,
    marginBottom: 6,
  },
  infoValue: {
    fontSize: 16,
    fontFamily: fontFamily.regular,
  },
  infoInput: {
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    fontSize: 16,
    fontFamily: fontFamily.regular,
  },
  editActions: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 20,
  },
  cancelButton: {
    flex: 1,
    borderColor: colors.textSecondary,
  },
  saveButton: {
    flex: 1,
  },
  profileActions: {
    gap: 12,
    marginTop: 20,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    ...shadowStyles.small,
  },
  logoutButton: {
    borderWidth: 1,
    borderColor: colors.error + '30',
    backgroundColor: colors.error + '08',
  },
  actionButtonText: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    fontFamily: fontFamily.medium,
    marginLeft: 12,
  },
});


import * as TaskManager from 'expo-task-manager';
import * as BackgroundFetch from 'expo-background-fetch';
import * as Location from 'expo-location';
import AsyncStorage from '@react-native-async-storage/async-storage';

const BACKGROUND_FETCH_TASK = 'background-fetch';
const LOCATION_TASK_NAME = 'background-location';

// Define the background fetch task
TaskManager.defineTask(BACKGROUND_FETCH_TASK, async () => {
  console.log('Background fetch task executed');

  try {
    // Check if driver has an active shift
    const currentShift = await AsyncStorage.getItem('currentShift');
    if (currentShift) {
      const shiftData = JSON.parse(currentShift);
      if (shiftData.isActive) {
        console.log('Driver has active shift, maintaining background services');
        return BackgroundFetch.BackgroundFetchResult.NewData;
      }
    }

    return BackgroundFetch.BackgroundFetchResult.NoData;
  } catch (error) {
    console.error('Background fetch error:', error);
    return BackgroundFetch.BackgroundFetchResult.Failed;
  }
});

// Define the location tracking task
TaskManager.defineTask(LOCATION_TASK_NAME, ({ data, error }) => {
  if (error) {
    console.error('Location task error:', error);
    return;
  }

  if (data) {
    const { locations } = data as any;
    console.log('Background location update:', locations);

    // Store the latest location for the active driver
    if (locations && locations.length > 0) {
      const location = locations[0];
      storeDriverLocation(location);
    }
  }
});

const storeDriverLocation = async (location: any) => {
  try {
    const currentShift = await AsyncStorage.getItem('currentShift');
    if (currentShift) {
      const shiftData = JSON.parse(currentShift);
      if (shiftData.isActive) {
        // Store location with timestamp
        const locationData = {
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
          timestamp: new Date().toISOString(),
          driverId: shiftData.driverId,
          lineId: shiftData.lineId,
        };

        // Store current location
        await AsyncStorage.setItem('driverCurrentLocation', JSON.stringify(locationData));

        // Store in location history (keep last 10 locations)
        const historyKey = `locationHistory_${shiftData.driverId}`;
        const existingHistory = await AsyncStorage.getItem(historyKey);
        const history = existingHistory ? JSON.parse(existingHistory) : [];

        history.unshift(locationData);
        if (history.length > 10) {
          history.pop();
        }

        await AsyncStorage.setItem(historyKey, JSON.stringify(history));

        console.log('Driver location stored:', locationData);
      }
    }
  } catch (error) {
    console.error('Error storing driver location:', error);
  }
};

export const BackgroundService = {
  async initialize() {
    try {
      // Register background fetch
      await BackgroundFetch.registerTaskAsync(BACKGROUND_FETCH_TASK, {
        minimumInterval: 15000, // 15 seconds
        stopOnTerminate: false,
        startOnBoot: true,
      });

      console.log('Background services initialized');
    } catch (error) {
      console.error('Error initializing background services:', error);
    }
  },

  async startLocationTracking() {
    try {
      // Request location permissions
      const { status } = await Location.requestBackgroundPermissionsAsync();
      if (status !== 'granted') {
        console.log('Background location permission not granted');
        return false;
      }

      // Start location tracking
      await Location.startLocationUpdatesAsync(LOCATION_TASK_NAME, {
        accuracy: Location.Accuracy.High,
        timeInterval: 15000, // Update every 15 seconds
        distanceInterval: 10, // Update every 10 meters
        foregroundService: {
          notificationTitle: 'Bus Tracker Morocco',
          notificationBody: 'Tracking your location for passengers',
          notificationColor: '#004D6B',
        },
      });

      console.log('Location tracking started');
      return true;
    } catch (error) {
      console.error('Error starting location tracking:', error);
      return false;
    }
  },

  async stopLocationTracking() {
    try {
      const isRegistered = await TaskManager.isTaskRegisteredAsync(LOCATION_TASK_NAME);
      if (isRegistered) {
        await Location.stopLocationUpdatesAsync(LOCATION_TASK_NAME);
        console.log('Location tracking stopped');
      }
    } catch (error) {
      console.error('Error stopping location tracking:', error);
    }
  },

  async getCurrentDriverLocation(driverId: string) {
    try {
      const locationData = await AsyncStorage.getItem('driverCurrentLocation');
      if (locationData) {
        const location = JSON.parse(locationData);
        if (location.driverId === driverId) {
          return location;
        }
      }
      return null;
    } catch (error) {
      console.error('Error getting driver location:', error);
      return null;
    }
  },

  async getDriverLocationHistory(driverId: string) {
    try {
      const historyKey = `locationHistory_${driverId}`;
      const historyData = await AsyncStorage.getItem(historyKey);
      return historyData ? JSON.parse(historyData) : [];
    } catch (error) {
      console.error('Error getting location history:', error);
      return [];
    }
  },

  async getBusLocationsForLine(lineId: string) {
    try {
      // Get all active shifts for this line
      const selectedLinesData = await AsyncStorage.getItem('selectedBusLines');
      const selectedLines = selectedLinesData ? JSON.parse(selectedLinesData) : {};

      const busLocations = [];

      for (const [currentLineId, lineData] of Object.entries(selectedLines)) {
        if (currentLineId === lineId && lineData) {
          const driverId = (lineData as any).driverId;
          const location = await this.getCurrentDriverLocation(driverId);
          if (location) {
            busLocations.push({
              driverId,
              driverName: (lineData as any).driverName,
              location,
            });
          }
        }
      }

      return busLocations;
    } catch (error) {
      console.error('Error getting bus locations for line:', error);
      return [];
    }
  },

  async isLocationTrackingActive() {
    try {
      const isRegistered = await TaskManager.isTaskRegisteredAsync(LOCATION_TASK_NAME);
      return isRegistered;
    } catch (error) {
      console.error('Error checking location tracking status:', error);
      return false;
    }
  },

  async clearLocationHistory() {
    try {
      const currentShift = await AsyncStorage.getItem('currentShift');
      if (currentShift) {
        const shiftData = JSON.parse(currentShift);
        const historyKey = `locationHistory_${shiftData.driverId}`;
        await AsyncStorage.removeItem(historyKey);
        await AsyncStorage.removeItem('driverCurrentLocation');
        console.log('Location history cleared');
      }
    } catch (error) {
      console.error('Error clearing location history:', error);
    }
  }
};

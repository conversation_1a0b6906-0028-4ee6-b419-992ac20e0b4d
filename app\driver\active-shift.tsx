
import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { IconSymbol } from '@/components/IconSymbol';
import { colors, commonStyles, buttonStyles } from '@/styles/commonStyles';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { BackgroundService } from '@/services/backgroundService';

export default function ActiveShift() {
  const router = useRouter();
  const [shiftDuration, setShiftDuration] = useState(0);
  const [isTracking, setIsTracking] = useState(true);
  const [shiftData, setShiftData] = useState<any>(null);
  const [locationPermission, setLocationPermission] = useState<boolean | null>(null);

  useEffect(() => {
    loadShiftData();
    initializeLocationTracking();

    const interval = setInterval(() => {
      setShiftDuration(prev => prev + 1);
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const loadShiftData = async () => {
    try {
      const currentShift = await AsyncStorage.getItem('currentShift');
      if (currentShift) {
        const shift = JSON.parse(currentShift);
        setShiftData(shift);

        // Calculate duration from start time
        const startTime = new Date(shift.startTime);
        const now = new Date();
        const durationInSeconds = Math.floor((now.getTime() - startTime.getTime()) / 1000);
        setShiftDuration(durationInSeconds);
      }
    } catch (error) {
      console.error('Error loading shift data:', error);
    }
  };

  const initializeLocationTracking = async () => {
    try {
      console.log('Initializing location tracking...');

      // Initialize background service first
      await BackgroundService.initialize();

      const isActive = await BackgroundService.isLocationTrackingActive();
      console.log('Location tracking active:', isActive);
      setIsTracking(isActive);

      if (!isActive) {
        const success = await BackgroundService.startLocationTracking();
        console.log('Location tracking started:', success);
        setLocationPermission(success);
        setIsTracking(success);
      } else {
        setLocationPermission(true);
      }
    } catch (error) {
      console.error('Error initializing location tracking:', error);
      setLocationPermission(false);
      setIsTracking(false);
    }
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleEndShift = () => {
    Alert.alert(
      'End Shift',
      'Are you sure you want to end your shift? This will make your line available for other drivers and stop location tracking.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'End Shift',
          style: 'destructive',
          onPress: async () => {
            console.log('Ending shift');

            try {
              // Stop location tracking
              await BackgroundService.stopLocationTracking();

              // Clear location history
              await BackgroundService.clearLocationHistory();

              // Release the selected line
              const busLinesStatus = await AsyncStorage.getItem('busLinesStatus');
              if (busLinesStatus && shiftData) {
                const lines = JSON.parse(busLinesStatus);
                const updatedLines = lines.map((line: any) =>
                  line.id === shiftData.lineId
                    ? { ...line, isSelected: false, selectedBy: undefined }
                    : line
                );

                await AsyncStorage.setItem('busLinesStatus', JSON.stringify(updatedLines));
              }

              // Clear current shift
              await AsyncStorage.removeItem('currentShift');

              Alert.alert(
                'Shift Ended',
                'Your shift has been ended successfully. Thank you for your service!',
                [
                  {
                    text: 'OK',
                    onPress: () => router.push('/driver/line-selection')
                  }
                ]
              );
            } catch (error) {
              console.error('Error ending shift:', error);
              Alert.alert('Error', 'Failed to end shift properly. Please try again.');
            }
          }
        }
      ]
    );
  };

  const toggleTracking = async () => {
    const newTrackingState = !isTracking;
    console.log('Toggling tracking to:', newTrackingState);

    try {
      if (newTrackingState) {
        console.log('Starting location tracking...');
        const success = await BackgroundService.startLocationTracking();
        console.log('Location tracking start result:', success);
        setIsTracking(success);
        setLocationPermission(success);

        if (!success) {
          Alert.alert(
            'Location Permission Required',
            'Unable to start location tracking. Please check your location permissions in device settings.',
            [
              { text: 'OK' }
            ]
          );
        }
      } else {
        console.log('Stopping location tracking...');
        await BackgroundService.stopLocationTracking();
        setIsTracking(false);
        console.log('Location tracking stopped');
      }
    } catch (error) {
      console.error('Error toggling tracking:', error);
      Alert.alert('Error', 'Failed to toggle location tracking. Please try again.');
    }
  };

  if (!shiftData) {
    return (
      <View style={[commonStyles.container, { backgroundColor: colors.background }]}>
        <View style={[commonStyles.centerContent]}>
          <Text style={[commonStyles.text, { color: colors.text }]}>Loading shift data...</Text>
        </View>
      </View>
    );
  }

  return (
    <>
      <Stack.Screen
        options={{
          title: 'Active Shift',
          headerStyle: {
            backgroundColor: colors.primary,
          },
          headerTintColor: colors.card,
          headerLeft: () => null, // Disable back button during active shift
        }}
      />
      <View style={[commonStyles.container, { backgroundColor: colors.background }]}>
        <View style={commonStyles.content}>
          <View style={styles.statusContainer}>
            <View style={[styles.statusIndicator, { backgroundColor: isTracking ? colors.success : colors.error }]}>
              <IconSymbol
                name={isTracking ? "location.fill" : "location.slash.fill"}
                size={24}
                color={colors.card}
              />
            </View>
            <Text style={[styles.statusText, { color: colors.text }]}>
              {isTracking ? 'Tracking Active' : 'Tracking Paused'}
            </Text>
            {locationPermission === false && (
              <Text style={[styles.permissionWarning, { color: colors.error }]}>
                Location permission denied
              </Text>
            )}
            {isTracking && (
              <Text style={[styles.backgroundInfo, { color: colors.primary }]}>
                📱 App continues tracking in background
              </Text>
            )}
          </View>

          <View style={[commonStyles.card, { backgroundColor: colors.card }]}>
            <Text style={[styles.cardTitle, { color: colors.text }]}>Shift Information</Text>

            <View style={styles.infoRow}>
              <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>Line:</Text>
              <Text style={[styles.infoValue, { color: colors.text }]}>{shiftData.lineId}</Text>
            </View>

            <View style={styles.infoRow}>
              <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>Driver:</Text>
              <Text style={[styles.infoValue, { color: colors.text }]}>{shiftData.driverName}</Text>
            </View>

            <View style={styles.infoRow}>
              <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>Start Time:</Text>
              <Text style={[styles.infoValue, { color: colors.text }]}>
                {new Date(shiftData.startTime).toLocaleTimeString()}
              </Text>
            </View>
          </View>

          <View style={[commonStyles.card, { backgroundColor: colors.card }]}>
            <Text style={[styles.cardTitle, { color: colors.text }]}>Shift Duration</Text>
            <Text style={[styles.durationText, { color: colors.primary }]}>
              {formatDuration(shiftDuration)}
            </Text>
            <Text style={[styles.durationSubtext, { color: colors.textSecondary }]}>
              Continues counting even when app is closed
            </Text>
          </View>

          <View style={styles.actionContainer}>
            <TouchableOpacity
              style={[
                buttonStyles.secondary,
                {
                  backgroundColor: isTracking ? colors.accent : colors.success,
                  marginBottom: 16,
                  opacity: locationPermission === false ? 0.5 : 1
                }
              ]}
              onPress={toggleTracking}
              disabled={locationPermission === false}
            >
              <Text style={[commonStyles.buttonText, { color: colors.card }]}>
                {isTracking ? 'Pause Tracking' : 'Resume Tracking'}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[buttonStyles.outline, { borderColor: colors.error }]}
              onPress={handleEndShift}
            >
              <Text style={[commonStyles.buttonTextOutline, { color: colors.error }]}>
                End Shift
              </Text>
            </TouchableOpacity>
          </View>

          <View style={styles.infoSection}>
            <Text style={[commonStyles.textSecondary, { textAlign: 'center', fontSize: 12 }]}>
              {isTracking
                ? 'Your location is being shared with passengers and tracked in the background. The app will continue working even when closed.'
                : 'Location sharing is paused. Passengers cannot see your current location.'
              }
            </Text>
          </View>
        </View>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  statusContainer: {
    alignItems: 'center',
    marginBottom: 30,
    marginTop: 20,
  },
  statusIndicator: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  statusText: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  permissionWarning: {
    fontSize: 14,
    marginTop: 4,
    textAlign: 'center',
  },
  backgroundInfo: {
    fontSize: 12,
    marginTop: 4,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  infoLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
  infoValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  durationText: {
    fontSize: 32,
    fontWeight: 'bold',
    textAlign: 'center',
    marginTop: 8,
  },
  durationSubtext: {
    fontSize: 12,
    textAlign: 'center',
    marginTop: 4,
    fontStyle: 'italic',
  },
  actionContainer: {
    marginTop: 30,
  },
  infoSection: {
    marginTop: 'auto',
    paddingTop: 20,
  },
});

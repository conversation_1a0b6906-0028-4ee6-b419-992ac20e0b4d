
import React, { useState } from 'react';
import { 
  View, 
  Text, 
  TouchableOpacity, 
  StyleSheet, 
  ScrollView, 
  TextInput,
  Platform 
} from 'react-native';
import { IconSymbol } from '@/components/IconSymbol';
import { Stack, useRouter } from 'expo-router';
import { colors, commonStyles, buttonStyles, shadowStyles, fontFamily } from '@/styles/commonStyles';
import { cities } from '@/data/cities';
import Animated, { FadeInDown, FadeInUp } from 'react-native-reanimated';
import i18n from '@/localization/i18n';

export default function CitySelection() {
  const router = useRouter();
  const [selectedCity, setSelectedCity] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState('');

  const filteredCities = cities.filter(city =>
    city.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    city.arabicName.includes(searchQuery) ||
    city.region.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleCitySelect = (cityId: string) => {
    console.log('Selected city:', cityId);
    setSelectedCity(cityId);
  };

  const handleContinue = () => {
    if (!selectedCity) {
      console.log('No city selected');
      return;
    }
    
    const city = cities.find(c => c.id === selectedCity);
    console.log('Continuing with city:', city?.name);
    
    router.push({
      pathname: '/passenger/line-input',
      params: { 
        city: selectedCity, 
        cityName: city?.name || '',
        region: city?.region || ''
      }
    });
  };

  const handleBack = () => {
    console.log('Going back to home');
    router.back();
  };

  return (
    <>
      <Stack.Screen
        options={{
          title: i18n.t('selectYourCity'),
          headerShown: false,
        }}
      />
      
      <View style={[commonStyles.container, { backgroundColor: colors.background }]}>
        {/* Custom Header */}
        <View style={styles.customHeader}>
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <IconSymbol name="chevron.left" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            {i18n.t('selectYourCity')}
          </Text>
        </View>

        <ScrollView 
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Header Section */}
          <Animated.View 
            entering={FadeInUp.delay(200).duration(500)}
            style={styles.headerSection}
          >
            <IconSymbol name="location.circle.fill" size={60} color={colors.passenger} />
            <Text style={[commonStyles.subtitle, styles.title]}>
              {i18n.t('chooseYourCity')}
            </Text>
            <Text style={[commonStyles.textSecondary, styles.subtitle]}>
              {i18n.t('selectCityDescription')}
            </Text>
          </Animated.View>

          {/* Search Bar */}
          <Animated.View 
            entering={FadeInDown.delay(300).duration(500)}
            style={styles.searchContainer}
          >
            <View style={styles.searchInputContainer}>
              <IconSymbol name="magnifyingglass" size={20} color={colors.textSecondary} />
              <TextInput
                style={[styles.searchInput, { color: colors.text }]}
                placeholder={i18n.t('searchCities')}
                placeholderTextColor={colors.textSecondary}
                value={searchQuery}
                onChangeText={setSearchQuery}
              />
              {searchQuery.length > 0 && (
                <TouchableOpacity onPress={() => setSearchQuery('')}>
                  <IconSymbol name="xmark.circle.fill" size={20} color={colors.textSecondary} />
                </TouchableOpacity>
              )}
            </View>
          </Animated.View>

          {/* Cities List */}
          <View style={styles.citiesContainer}>
            {filteredCities.map((city, index) => (
              <Animated.View
                key={city.id}
                entering={FadeInDown.delay(400 + index * 100).duration(500)}
              >
                <TouchableOpacity
                  style={[
                    styles.cityCard,
                    { backgroundColor: colors.card },
                    selectedCity === city.id && styles.selectedCityCard
                  ]}
                  onPress={() => handleCitySelect(city.id)}
                  activeOpacity={0.7}
                >
                  <View style={styles.cityContent}>
                    <View style={styles.cityInfo}>
                      <Text style={[
                        styles.cityName, 
                        { color: colors.text },
                        selectedCity === city.id && { color: colors.passenger }
                      ]}>
                        {city.name}
                      </Text>
                      <Text style={[styles.cityArabicName, { color: colors.textSecondary }]}>
                        {city.arabicName}
                      </Text>
                      <Text style={[styles.cityRegion, { color: colors.textSecondary }]}>
                        {city.region}
                      </Text>
                      <View style={styles.companiesContainer}>
                        <IconSymbol name="bus" size={14} color={colors.accent} />
                        <Text style={[styles.companiesText, { color: colors.textSecondary }]}>
                          {city.busCompanies.length} bus companies
                        </Text>
                      </View>
                    </View>
                    
                    <View style={styles.cityActions}>
                      {selectedCity === city.id ? (
                        <View style={[styles.selectedIcon, { backgroundColor: colors.passenger }]}>
                          <IconSymbol name="checkmark" size={20} color={colors.card} />
                        </View>
                      ) : (
                        <View style={[styles.selectIcon, { borderColor: colors.border }]}>
                          <IconSymbol name="chevron.right" size={16} color={colors.textSecondary} />
                        </View>
                      )}
                    </View>
                  </View>
                </TouchableOpacity>
              </Animated.View>
            ))}
          </View>

          {filteredCities.length === 0 && (
            <Animated.View 
              entering={FadeInDown.delay(500).duration(500)}
              style={styles.noResultsContainer}
            >
              <IconSymbol name="magnifyingglass" size={48} color={colors.textSecondary} />
              <Text style={[styles.noResultsText, { color: colors.textSecondary }]}>
                {i18n.t('noResultsFound')} "{searchQuery}"
              </Text>
              <Text style={[styles.noResultsSubtext, { color: colors.textSecondary }]}>
                {i18n.t('tryDifferentSearch')}
              </Text>
            </Animated.View>
          )}
        </ScrollView>

        {/* Continue Button */}
        {selectedCity && (
          <Animated.View 
            entering={FadeInUp.delay(600).duration(500)}
            style={styles.continueContainer}
          >
            <TouchableOpacity
              style={[buttonStyles.passenger, styles.continueButton]}
              onPress={handleContinue}
            >
              <Text style={[commonStyles.buttonText, styles.continueButtonText]}>
                {i18n.t('continueToLines')}
              </Text>
              <IconSymbol name="arrow.right" size={20} color={colors.card} />
            </TouchableOpacity>
          </Animated.View>
        )}
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  customHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingHorizontal: 20,
    paddingBottom: 16,
    backgroundColor: colors.background,
    ...shadowStyles.small,
  },
  backButton: {
    padding: 8,
    marginLeft: -8,
    marginRight: 12,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    fontFamily: fontFamily.bold,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 120,
  },
  headerSection: {
    alignItems: 'center',
    padding: 20,
    paddingTop: 30,
  },
  title: {
    marginTop: 16,
    marginBottom: 8,
  },
  subtitle: {
    textAlign: 'center',
    paddingHorizontal: 20,
  },
  searchContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.card,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
    ...shadowStyles.small,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    fontFamily: fontFamily.regular,
    ...Platform.select({
      web: { outline: 'none' },
    }),
  },
  citiesContainer: {
    paddingHorizontal: 20,
    gap: 12,
  },
  cityCard: {
    borderRadius: 16,
    padding: 20,
    borderWidth: 2,
    borderColor: 'transparent',
    ...shadowStyles.small,
  },
  selectedCityCard: {
    borderColor: colors.passenger,
    backgroundColor: colors.passenger + '08',
    ...shadowStyles.medium,
  },
  cityContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  cityInfo: {
    flex: 1,
  },
  cityName: {
    fontSize: 18,
    fontWeight: Platform.OS === 'ios' ? '600' : 'bold',
    fontFamily: fontFamily.medium,
    marginBottom: 4,
  },
  cityArabicName: {
    fontSize: 16,
    marginBottom: 4,
    textAlign: 'right',
  },
  cityRegion: {
    fontSize: 14,
    marginBottom: 8,
  },
  companiesContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  companiesText: {
    fontSize: 12,
  },
  cityActions: {
    marginLeft: 16,
  },
  selectedIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  noResultsContainer: {
    alignItems: 'center',
    padding: 40,
  },
  noResultsText: {
    fontSize: 16,
    fontWeight: '500',
    fontFamily: fontFamily.medium,
    marginTop: 16,
    marginBottom: 8,
  },
  noResultsSubtext: {
    fontSize: 14,
    textAlign: 'center',
  },
  continueContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: colors.background,
    padding: 20,
    paddingBottom: Platform.OS === 'ios' ? 40 : 30,
    ...shadowStyles.large,
  },
  continueButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  continueButtonText: {
    fontSize: 16,
  },
});

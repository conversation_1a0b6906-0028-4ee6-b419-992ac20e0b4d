
import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Platform,
  Dimensions
} from "react-native";
import { useTheme } from "@react-navigation/native";
import { useRouter } from "expo-router";
import { Stack } from "expo-router";
import { IconSymbol } from "@/components/IconSymbol";
import { colors, commonStyles, buttonStyles, shadowStyles, fontFamily } from "@/styles/commonStyles";
import SideMenu from "@/components/SideMenu";
import AsyncStorage from '@react-native-async-storage/async-storage';
import Animated, {
  FadeInDown,
  FadeInUp,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming
} from 'react-native-reanimated';
import i18n from '@/localization/i18n';

const { width: screenWidth } = Dimensions.get('window');

export default function HomeScreen() {
  const theme = useTheme();
  const router = useRouter();
  const [isMenuVisible, setIsMenuVisible] = useState(false);
  const scaleDriver = useSharedValue(1);
  const scalePassenger = useSharedValue(1);

  useEffect(() => {
    checkOnboarding();
  }, []);

  const checkOnboarding = async () => {
    try {
      const hasSeenOnboarding = await AsyncStorage.getItem('hasSeenOnboarding');
      if (!hasSeenOnboarding) {
        console.log('First time user, showing onboarding');
        router.replace('/onboarding');
      }
    } catch (error) {
      console.error('Error checking onboarding status:', error);
    }
  };

  const handleUserTypeSelect = (route: string) => {
    console.log('Navigating to:', route);

    // Add haptic feedback and animation
    const scale = route.includes('driver') ? scaleDriver : scalePassenger;
    scale.value = withSpring(0.95, { duration: 100 }, () => {
      scale.value = withSpring(1, { duration: 100 });
    });

    // Navigate with slight delay for animation
    setTimeout(() => {
      router.push(route);
    }, 150);
  };

  const handleMenuToggle = () => {
    console.log('Toggling menu');
    setIsMenuVisible(!isMenuVisible);
  };

  const driverAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scaleDriver.value }],
  }));

  const passengerAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scalePassenger.value }],
  }));

  return (
    <>
      <Stack.Screen
        options={{
          title: i18n.t('appTitle'),
          headerShown: false,
        }}
      />

      <View style={[commonStyles.container, { backgroundColor: colors.background }]}>
        {/* Header with Menu Button */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.menuButton}
            onPress={handleMenuToggle}
            activeOpacity={0.7}
          >
            <View style={styles.menuButtonBackground}>
              <IconSymbol name="line.horizontal.3" size={20} color={colors.card} />
            </View>
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            {i18n.t('appTitle')}
          </Text>
        </View>

        <ScrollView
          style={[commonStyles.container, { backgroundColor: colors.background }]}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.container}>
            {/* Header Section */}
            <Animated.View
              entering={FadeInUp.delay(200).duration(600)}
              style={styles.headerSection}
            >
              <View style={styles.iconContainer}>
                <IconSymbol name="bus" size={80} color={colors.primary} />
              </View>
              <Text style={[commonStyles.title, styles.appTitle]}>
                {i18n.t('appTitle')}
              </Text>
              <Text style={[commonStyles.textSecondary, styles.appSubtitle]}>
                {i18n.t('appSubtitle')}
              </Text>
            </Animated.View>

            {/* User Type Selection */}
            <View style={styles.selectionContainer}>
              <Animated.View
                entering={FadeInDown.delay(400).duration(600)}
                style={styles.cardContainer}
              >
                <Animated.View style={driverAnimatedStyle}>
                  <TouchableOpacity
                    style={[styles.userTypeCard, styles.driverCard]}
                    onPress={() => handleUserTypeSelect('/driver/login')}
                    activeOpacity={0.8}
                  >
                    <View style={styles.cardContent}>
                      <View style={[styles.cardIcon, styles.driverIcon]}>
                        <IconSymbol name="person.fill" size={40} color={colors.card} />
                      </View>
                      <Text style={[styles.cardTitle, { color: colors.card }]}>
                        {i18n.t('imADriver')}
                      </Text>
                      <Text style={[styles.cardDescription, { color: colors.card }]}>
                        {i18n.t('driverDescription')}
                      </Text>
                      <View style={styles.cardFeatures}>
                        <View style={styles.featureItem}>
                          <IconSymbol name="checkmark.circle.fill" size={16} color={colors.card} />
                          <Text style={[styles.featureText, { color: colors.card }]}>
                            {i18n.t('selectLine')}
                          </Text>
                        </View>
                        <View style={styles.featureItem}>
                          <IconSymbol name="checkmark.circle.fill" size={16} color={colors.card} />
                          <Text style={[styles.featureText, { color: colors.card }]}>
                            {i18n.t('trackRoute')}
                          </Text>
                        </View>
                        <View style={styles.featureItem}>
                          <IconSymbol name="checkmark.circle.fill" size={16} color={colors.card} />
                          <Text style={[styles.featureText, { color: colors.card }]}>
                            {i18n.t('manageShifts')}
                          </Text>
                        </View>
                      </View>
                    </View>
                    <View style={styles.cardArrow}>
                      <IconSymbol name="chevron.right" size={24} color={colors.card} />
                    </View>
                  </TouchableOpacity>
                </Animated.View>
              </Animated.View>

              <Animated.View
                entering={FadeInDown.delay(600).duration(600)}
                style={styles.cardContainer}
              >
                <Animated.View style={passengerAnimatedStyle}>
                  <TouchableOpacity
                    style={[styles.userTypeCard, styles.passengerCard]}
                    onPress={() => handleUserTypeSelect('/passenger/city-selection')}
                    activeOpacity={0.8}
                  >
                    <View style={styles.cardContent}>
                      <View style={[styles.cardIcon, styles.passengerIcon]}>
                        <IconSymbol name="location.fill" size={40} color={colors.card} />
                      </View>
                      <Text style={[styles.cardTitle, { color: colors.card }]}>
                        {i18n.t('imAPassenger')}
                      </Text>
                      <Text style={[styles.cardDescription, { color: colors.card }]}>
                        {i18n.t('passengerDescription')}
                      </Text>
                      <View style={styles.cardFeatures}>
                        <View style={styles.featureItem}>
                          <IconSymbol name="checkmark.circle.fill" size={16} color={colors.card} />
                          <Text style={[styles.featureText, { color: colors.card }]}>
                            {i18n.t('findNearbyBuses')}
                          </Text>
                        </View>
                        <View style={styles.featureItem}>
                          <IconSymbol name="checkmark.circle.fill" size={16} color={colors.card} />
                          <Text style={[styles.featureText, { color: colors.card }]}>
                            {i18n.t('realTimeTracking')}
                          </Text>
                        </View>
                        <View style={styles.featureItem}>
                          <IconSymbol name="checkmark.circle.fill" size={16} color={colors.card} />
                          <Text style={[styles.featureText, { color: colors.card }]}>
                            {i18n.t('etaEstimates')}
                          </Text>
                        </View>
                      </View>
                    </View>
                    <View style={styles.cardArrow}>
                      <IconSymbol name="chevron.right" size={24} color={colors.card} />
                    </View>
                  </TouchableOpacity>
                </Animated.View>
              </Animated.View>
            </View>

            {/* Info Section */}
            <Animated.View
              entering={FadeInUp.delay(800).duration(600)}
              style={styles.infoSection}
            >
              <View style={[commonStyles.card, styles.infoCard]}>
                <IconSymbol name="info.circle.fill" size={24} color={colors.primary} />
                <Text style={[styles.infoText, { color: colors.text }]}>
                  This app provides real-time tracking for public buses across Morocco.
                  Drivers can start their shifts and passengers can track bus locations live.
                </Text>
              </View>
            </Animated.View>
          </View>
        </ScrollView>

        {/* Side Menu */}
        <SideMenu
          isVisible={isMenuVisible}
          onClose={() => setIsMenuVisible(false)}
        />
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingHorizontal: 20,
    paddingBottom: 16,
    backgroundColor: colors.background,
    ...shadowStyles.small,
  },
  menuButton: {
    marginRight: 16,
  },
  menuButtonBackground: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    ...shadowStyles.small,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    fontFamily: fontFamily.bold,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: Platform.OS === 'ios' ? 120 : 140,
  },
  container: {
    flex: 1,
    padding: 20,
  },
  headerSection: {
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 40,
  },
  iconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: colors.primary + '15',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
    ...shadowStyles.medium,
  },
  appTitle: {
    fontSize: 28,
    marginBottom: 12,
    color: colors.text,
    fontFamily: fontFamily.bold,
  },
  appSubtitle: {
    textAlign: 'center',
    paddingHorizontal: 20,
    lineHeight: 22,
  },
  selectionContainer: {
    flex: 1,
    gap: 20,
  },
  cardContainer: {
    marginBottom: 8,
  },
  userTypeCard: {
    borderRadius: 20,
    padding: 24,
    flexDirection: 'row',
    alignItems: 'center',
    minHeight: 160,
    ...shadowStyles.large,
  },
  driverCard: {
    backgroundColor: colors.primary,
  },
  passengerCard: {
    backgroundColor: colors.passenger,
  },
  cardContent: {
    flex: 1,
  },
  cardIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  driverIcon: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  passengerIcon: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  cardTitle: {
    fontSize: 22,
    fontWeight: Platform.OS === 'ios' ? '700' : 'bold',
    fontFamily: fontFamily.bold,
    marginBottom: 8,
  },
  cardDescription: {
    fontSize: 14,
    opacity: 0.9,
    marginBottom: 16,
    lineHeight: 20,
  },
  cardFeatures: {
    gap: 6,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  featureText: {
    fontSize: 13,
    opacity: 0.9,
  },
  cardArrow: {
    marginLeft: 16,
    opacity: 0.8,
  },
  infoSection: {
    marginTop: 32,
    marginBottom: 20,
  },
  infoCard: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
    backgroundColor: colors.primary + '08',
    borderWidth: 1,
    borderColor: colors.primary + '20',
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    lineHeight: 20,
  },
});

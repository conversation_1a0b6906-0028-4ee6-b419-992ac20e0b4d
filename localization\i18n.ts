
import { I18n } from 'i18n-js';
import * as Localization from 'expo-localization';

const i18n = new I18n({
  en: {
    // Common
    back: 'Back',
    continue: 'Continue',
    cancel: 'Cancel',
    ok: 'OK',
    error: 'Error',
    success: 'Success',
    loading: 'Loading...',
    refresh: 'Refresh',
    search: 'Search',
    
    // Home Screen
    appTitle: 'Bus Tracker Morocco',
    appSubtitle: 'Real-time bus tracking for Morocco\'s public transportation',
    imADriver: 'I\'m a Driver',
    imAPassenger: 'I\'m a Passenger',
    driverDescription: 'Start your shift and begin tracking your bus route',
    passengerDescription: 'Track buses in real-time and plan your journey',
    selectLine: 'Select your line',
    trackRoute: 'Track your route',
    manageShifts: 'Manage shifts',
    findNearbyBuses: 'Find nearby buses',
    realTimeTracking: 'Real-time tracking',
    etaEstimates: 'ETA estimates',
    
    // Driver Registration
    driverRegistration: 'Driver Registration',
    createDriverAccount: 'Create your driver account to start working',
    fullName: 'Full Name',
    email: 'Email',
    phoneNumber: 'Phone Number',
    busCompany: 'Bus Company',
    city: 'City',
    driverLicenseNumber: 'Driver License Number',
    busNumber: 'Bus Number (Optional)',
    password: 'Password',
    confirmPassword: 'Confirm Password',
    createAccount: 'Create Account',
    creatingAccount: 'Creating Account...',
    registrationSuccessful: 'Registration Successful',
    registrationSuccessMessage: 'Your driver account has been created successfully. You can now log in.',
    
    // OTP Verification
    otpVerification: 'OTP Verification',
    otpSent: 'We\'ve sent a verification code to your phone number',
    enterOtp: 'Enter verification code',
    verifyCode: 'Verify Code',
    resendCode: 'Resend Code',
    
    // City Selection
    selectYourCity: 'Select Your City',
    chooseYourCity: 'Choose Your City',
    selectCityDescription: 'Select the city where you want to track buses',
    searchCities: 'Search cities...',
    continueToLines: 'Continue to Bus Lines',
    noResultsFound: 'No cities found matching',
    tryDifferentSearch: 'Try searching with a different term',
    
    // Line Selection
    selectBusLine: 'Select Bus Line',
    searchLines: 'Search bus lines...',
    availableLines: 'Available Lines',
    selectedBy: 'Selected by',
    startShift: 'Start Shift',
    
    // Tracking Map
    interactiveMap: 'Interactive Map',
    mapNotSupported: 'Maps are fully supported in this app',
    tapToToggle: 'Tap to hide/show bus line info',
    youAreHere: 'You are here',
    locationAccessDenied: 'Location access denied',
    enable: 'Enable',
    lineVarieties: 'Line Varieties',
    busesInCity: 'buses in',
    nextStop: 'Next',
    eta: 'ETA',
    details: 'Details',
    arrivesIn: 'Arrives in',
    occupancy: 'Occupancy',
    updatingLocations: 'Updating locations...',
    
    // Profile
    profile: 'Profile',
    editProfile: 'Edit Profile',
    driverInfo: 'Driver Information',
    
    // Onboarding
    welcome: 'Welcome to Bus Tracker Morocco',
    chooseUserType: 'Choose your user type to get started',
    driverSteps: 'Driver Steps',
    passengerSteps: 'Passenger Steps',
    howToUseAsDriver: 'How to use as Driver',
    howToUseAsPassenger: 'How to use as Passenger',
    
    // Validation Messages
    enterFullName: 'Please enter your full name',
    enterValidEmail: 'Please enter a valid email address',
    passwordTooShort: 'Password must be at least 6 characters long',
    passwordsDoNotMatch: 'Passwords do not match',
    enterPhoneNumber: 'Please enter your phone number',
    selectBusCompany: 'Please select your bus company',
    selectCity: 'Please select your city',
    enterLicenseNumber: 'Please enter your license number',
    noCitySelected: 'No city selected',
  },
  ar: {
    // Common
    back: 'رجوع',
    continue: 'متابعة',
    cancel: 'إلغاء',
    ok: 'موافق',
    error: 'خطأ',
    success: 'نجح',
    loading: 'جاري التحميل...',
    refresh: 'تحديث',
    search: 'بحث',
    
    // Home Screen
    appTitle: 'تتبع الحافلات المغرب',
    appSubtitle: 'تتبع الحافلات في الوقت الفعلي للنقل العام في المغرب',
    imADriver: 'أنا سائق',
    imAPassenger: 'أنا راكب',
    driverDescription: 'ابدأ نوبتك وابدأ في تتبع خط الحافلة الخاص بك',
    passengerDescription: 'تتبع الحافلات في الوقت الفعلي وخطط لرحلتك',
    selectLine: 'اختر خطك',
    trackRoute: 'تتبع طريقك',
    manageShifts: 'إدارة النوبات',
    findNearbyBuses: 'العثور على الحافلات القريبة',
    realTimeTracking: 'التتبع في الوقت الفعلي',
    etaEstimates: 'تقديرات وقت الوصول',
    
    // Driver Registration
    driverRegistration: 'تسجيل السائق',
    createDriverAccount: 'أنشئ حساب السائق الخاص بك لبدء العمل',
    fullName: 'الاسم الكامل',
    email: 'البريد الإلكتروني',
    phoneNumber: 'رقم الهاتف',
    busCompany: 'شركة الحافلات',
    city: 'المدينة',
    driverLicenseNumber: 'رقم رخصة السائق',
    busNumber: 'رقم الحافلة (اختياري)',
    password: 'كلمة المرور',
    confirmPassword: 'تأكيد كلمة المرور',
    createAccount: 'إنشاء حساب',
    creatingAccount: 'جاري إنشاء الحساب...',
    registrationSuccessful: 'تم التسجيل بنجاح',
    registrationSuccessMessage: 'تم إنشاء حساب السائق الخاص بك بنجاح. يمكنك الآن تسجيل الدخول.',
    
    // OTP Verification
    otpVerification: 'التحقق من الرمز',
    otpSent: 'لقد أرسلنا رمز التحقق إلى رقم هاتفك',
    enterOtp: 'أدخل رمز التحقق',
    verifyCode: 'تحقق من الرمز',
    resendCode: 'إعادة إرسال الرمز',
    
    // City Selection
    selectYourCity: 'اختر مدينتك',
    chooseYourCity: 'اختر مدينتك',
    selectCityDescription: 'اختر المدينة التي تريد تتبع الحافلات فيها',
    searchCities: 'البحث في المدن...',
    continueToLines: 'متابعة إلى خطوط الحافلات',
    noResultsFound: 'لم يتم العثور على مدن تطابق',
    tryDifferentSearch: 'جرب البحث بمصطلح مختلف',
    
    // Line Selection
    selectBusLine: 'اختر خط الحافلة',
    searchLines: 'البحث في خطوط الحافلات...',
    availableLines: 'الخطوط المتاحة',
    selectedBy: 'مختار من قبل',
    startShift: 'بدء النوبة',
    
    // Tracking Map
    interactiveMap: 'خريطة تفاعلية',
    mapNotSupported: 'الخرائط مدعومة بالكامل في هذا التطبيق',
    tapToToggle: 'اضغط لإخفاء/إظهار معلومات خط الحافلة',
    youAreHere: 'أنت هنا',
    locationAccessDenied: 'تم رفض الوصول للموقع',
    enable: 'تمكين',
    lineVarieties: 'أنواع الخط',
    busesInCity: 'حافلات في',
    nextStop: 'التالي',
    eta: 'وقت الوصول المتوقع',
    details: 'التفاصيل',
    arrivesIn: 'يصل في',
    occupancy: 'الإشغال',
    updatingLocations: 'تحديث المواقع...',
    
    // Profile
    profile: 'الملف الشخصي',
    editProfile: 'تعديل الملف الشخصي',
    driverInfo: 'معلومات السائق',
    
    // Onboarding
    welcome: 'مرحباً بك في تتبع الحافلات المغرب',
    chooseUserType: 'اختر نوع المستخدم للبدء',
    driverSteps: 'خطوات السائق',
    passengerSteps: 'خطوات الراكب',
    howToUseAsDriver: 'كيفية الاستخدام كسائق',
    howToUseAsPassenger: 'كيفية الاستخدام كراكب',
    
    // Validation Messages
    enterFullName: 'يرجى إدخال اسمك الكامل',
    enterValidEmail: 'يرجى إدخال عنوان بريد إلكتروني صالح',
    passwordTooShort: 'يجب أن تكون كلمة المرور 6 أحرف على الأقل',
    passwordsDoNotMatch: 'كلمات المرور غير متطابقة',
    enterPhoneNumber: 'يرجى إدخال رقم هاتفك',
    selectBusCompany: 'يرجى اختيار شركة الحافلات',
    selectCity: 'يرجى اختيار مدينتك',
    enterLicenseNumber: 'يرجى إدخال رقم رخصتك',
    noCitySelected: 'لم يتم اختيار مدينة',
  }
});

i18n.locale = Localization.getLocales()[0]?.languageCode ?? 'en';
i18n.enableFallback = true;

export default i18n;

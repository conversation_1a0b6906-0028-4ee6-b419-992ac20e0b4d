
import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Platform,
  Dimensions,
} from 'react-native';
import { IconSymbol } from '@/components/IconSymbol';
import { colors, commonStyles, shadowStyles, fontFamily } from '@/styles/commonStyles';
import { useRouter } from 'expo-router';
import Animated, { 
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
  runOnJS
} from 'react-native-reanimated';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import i18n from '@/localization/i18n';

const { width: screenWidth } = Dimensions.get('window');
const MENU_WIDTH = screenWidth * 0.8;

interface SideMenuProps {
  isVisible: boolean;
  onClose: () => void;
}

export default function SideMenu({ isVisible, onClose }: SideMenuProps) {
  const router = useRouter();
  
  const translateX = useSharedValue(-MENU_WIDTH);
  const overlayOpacity = useSharedValue(0);

  React.useEffect(() => {
    if (isVisible) {
      translateX.value = withSpring(0, { damping: 20, stiffness: 120 });
      overlayOpacity.value = withTiming(0.5, { duration: 300 });
    } else {
      translateX.value = withTiming(-MENU_WIDTH, { duration: 300 });
      overlayOpacity.value = withTiming(0, { duration: 300 });
    }
  }, [isVisible]);

  const menuAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: translateX.value }],
  }));

  const overlayAnimatedStyle = useAnimatedStyle(() => ({
    opacity: overlayOpacity.value,
  }));

  const panGesture = Gesture.Pan()
    .onUpdate((event) => {
      if (event.translationX < 0) {
        translateX.value = Math.max(-MENU_WIDTH, event.translationX);
      }
    })
    .onEnd((event) => {
      if (event.translationX < -MENU_WIDTH * 0.3 || event.velocityX < -500) {
        translateX.value = withTiming(-MENU_WIDTH, { duration: 300 });
        overlayOpacity.value = withTiming(0, { duration: 300 });
        runOnJS(onClose)();
      } else {
        translateX.value = withSpring(0, { damping: 20, stiffness: 120 });
      }
    });

  const handleMenuItemPress = (route: string) => {
    console.log('Navigating to:', route);
    onClose();
    setTimeout(() => {
      router.push(route);
    }, 300);
  };

  const handleOverlayPress = () => {
    onClose();
  };

  const menuItems = [
    {
      icon: 'house.fill',
      title: 'Home',
      route: '/(tabs)/(home)',
      color: colors.primary
    },
    {
      icon: 'person.fill',
      title: 'Driver Login',
      route: '/driver/login',
      color: colors.primary
    },
    {
      icon: 'location.circle.fill',
      title: 'Track Buses',
      route: '/passenger/city-selection',
      color: colors.passenger
    },
    {
      icon: 'info.circle.fill',
      title: 'How to Use',
      route: '/onboarding',
      color: colors.accent
    },
  ];

  if (!isVisible) return null;

  return (
    <View style={styles.container}>
      {/* Overlay */}
      <Animated.View style={[styles.overlay, overlayAnimatedStyle]}>
        <TouchableOpacity 
          style={styles.overlayTouchable}
          onPress={handleOverlayPress}
          activeOpacity={1}
        />
      </Animated.View>

      {/* Menu */}
      <GestureDetector gesture={panGesture}>
        <Animated.View style={[styles.menu, menuAnimatedStyle]}>
          <ScrollView 
            style={styles.menuContent}
            showsVerticalScrollIndicator={false}
          >
            {/* Header */}
            <View style={styles.menuHeader}>
              <View style={styles.logoContainer}>
                <IconSymbol name="bus" size={32} color={colors.primary} />
              </View>
              <Text style={[styles.appTitle, { color: colors.text }]}>
                Bus Tracker Morocco
              </Text>
              <Text style={[styles.appSubtitle, { color: colors.textSecondary }]}>
                Real-time bus tracking
              </Text>
            </View>

            {/* Menu Items */}
            <View style={styles.menuItems}>
              {menuItems.map((item, index) => (
                <TouchableOpacity
                  key={index}
                  style={[styles.menuItem, { backgroundColor: colors.card }]}
                  onPress={() => handleMenuItemPress(item.route)}
                  activeOpacity={0.7}
                >
                  <View style={[styles.menuItemIcon, { backgroundColor: item.color + '15' }]}>
                    <IconSymbol name={item.icon as any} size={20} color={item.color} />
                  </View>
                  <Text style={[styles.menuItemText, { color: colors.text }]}>
                    {item.title}
                  </Text>
                  <IconSymbol name="chevron.right" size={16} color={colors.textSecondary} />
                </TouchableOpacity>
              ))}
            </View>

            {/* Language Toggle */}
            <View style={styles.languageSection}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>
                Language / اللغة
              </Text>
              <View style={styles.languageButtons}>
                <TouchableOpacity
                  style={[
                    styles.languageButton,
                    { 
                      backgroundColor: i18n.locale === 'en' ? colors.primary : colors.card,
                      borderColor: colors.border
                    }
                  ]}
                  onPress={() => {
                    i18n.locale = 'en';
                    console.log('Language changed to English');
                  }}
                >
                  <Text style={[
                    styles.languageButtonText,
                    { color: i18n.locale === 'en' ? colors.card : colors.text }
                  ]}>
                    English
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.languageButton,
                    { 
                      backgroundColor: i18n.locale === 'ar' ? colors.primary : colors.card,
                      borderColor: colors.border
                    }
                  ]}
                  onPress={() => {
                    i18n.locale = 'ar';
                    console.log('Language changed to Arabic');
                  }}
                >
                  <Text style={[
                    styles.languageButtonText,
                    { color: i18n.locale === 'ar' ? colors.card : colors.text }
                  ]}>
                    العربية
                  </Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* Footer */}
            <View style={styles.menuFooter}>
              <Text style={[styles.footerText, { color: colors.textSecondary }]}>
                Version 1.0.0
              </Text>
              <Text style={[styles.footerText, { color: colors.textSecondary }]}>
                Made in Morocco 🇲🇦
              </Text>
            </View>
          </ScrollView>
        </Animated.View>
      </GestureDetector>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  overlayTouchable: {
    flex: 1,
  },
  menu: {
    position: 'absolute',
    top: 0,
    left: 0,
    bottom: 0,
    width: MENU_WIDTH,
    backgroundColor: colors.background,
    ...shadowStyles.large,
  },
  menuContent: {
    flex: 1,
  },
  menuHeader: {
    padding: 24,
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    alignItems: 'center',
  },
  logoContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: colors.primary + '15',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  appTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    fontFamily: fontFamily.bold,
    marginBottom: 4,
    textAlign: 'center',
  },
  appSubtitle: {
    fontSize: 14,
    textAlign: 'center',
  },
  menuItems: {
    padding: 16,
    gap: 8,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    ...shadowStyles.small,
  },
  menuItemIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  menuItemText: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    fontFamily: fontFamily.medium,
  },
  languageSection: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    fontFamily: fontFamily.medium,
    marginBottom: 12,
  },
  languageButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  languageButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
  },
  languageButtonText: {
    fontSize: 14,
    fontWeight: '500',
    fontFamily: fontFamily.medium,
  },
  menuFooter: {
    padding: 24,
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: colors.border,
    marginTop: 'auto',
  },
  footerText: {
    fontSize: 12,
    marginBottom: 4,
  },
});
